from django.db import models
from django.contrib.auth import get_user_model
from order.models import Order
from core.models import BaseModel

User = get_user_model()

class Payment(BaseModel):
    PAYMENT_STATUS_CHOICES = [
        ('held', 'Held'),
        ('ready', 'Ready'),
        ('transferred', 'Transferred'),
        ('cancelled', 'Cancelled'),
    ]

    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES)
    hold_date = models.DateTimeField()
    transfer_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Payment #{self.pk} - {self.payment_status}"

