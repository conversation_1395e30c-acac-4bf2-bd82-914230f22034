from django.urls import path
from .api.views import *

urlpatterns = [
    path('auth/register/', RegisterAPIView.as_view(), name='register'),
    path('auth/activate/<uidb64>/<token>/', ActivateAccountView.as_view() ,name='activate-account'),
    path('auth/login/', LoginAPIView.as_view(), name='login'),
    path('auth/profile/', GetUserProfileAPIView.as_view(), name='get-user-profile'),
    path('auth/profile/update/', UpdateUserProfileAPIView.as_view(), name='update-user-profile'),
    path('auth/forgot-password/', ForgotPasswordAPIView.as_view(), name='forgot-password'),
    path('auth/reset-password/<uidb64>/<token>/', ResetPasswordAPIView.as_view(), name='reset-password'),
]
