from django.core.mail import EmailMultiAlternatives
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.contrib.auth.tokens import default_token_generator

def send_activation_email(user, request):
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)

    react_base_url = "http://localhost:8000"
    activation_link = f"{react_base_url}/api/account/auth/activate/{uid}/{token}/"

    subject = 'Activate Your Account'
    from_email = '<EMAIL>'
    to_email = user.email

    text_content = f"Hi {user.fullname},\nPlease activate your account using this link:\n{activation_link}\n\nThis link will expire in 24 hours."

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Email Verification</title>
    </head>
    <body style="font-family: Arial, sans-serif; background-color: #f7f7f7; padding: 20px;">
        <div style="max-width: 600px; background-color: #fff; margin: auto; padding: 30px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333;">Welcome to Our Platform 👋</h2>
            <p style="font-size: 16px; color: #555;">Hi {user.fullname},</p>
            <p style="font-size: 16px; color: #555;">Please click the button below to verify your email address:</p>
            <a href="{activation_link}" style="display: inline-block; margin: 20px 0; padding: 12px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px;">Verify Email</a>
            <p style="font-size: 14px; color: #999;">If you did not sign up, please ignore this message.</p>
            <hr>
            <p style="font-size: 12px; color: #aaa;">&copy; 2025 Your Company. All rights reserved.</p>
        </div>
    </body>
    </html>
    """

    msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
    msg.attach_alternative(html_content, "text/html")
    msg.send()

def send_reset_password_email(user, request):
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)

    react_base_url = "http://localhost:8000"
    reset_link = f"{react_base_url}/reset-password/{uid}/{token}/"

    subject = 'Reset Your Password'
    from_email = '<EMAIL>'
    to_email = user.email

    text_content = f"Hi {user.fullname},\nReset your password using this link:\n{reset_link}\n\nThis link will expire in 24 hours."

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Password Reset</title>
    </head>
    <body style="font-family: Arial, sans-serif; background-color: #f7f7f7; padding: 20px;">
        <div style="max-width: 600px; background-color: #fff; margin: auto; padding: 30px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333;">Reset Your Password 🔐</h2>
            <p style="font-size: 16px; color: #555;">Hi {user.fullname},</p>
            <p style="font-size: 16px; color: #555;">You requested a password reset. Click the button below to set a new password:</p>
            <a href="{reset_link}" style="display: inline-block; margin: 20px 0; padding: 12px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
            <p style="font-size: 14px; color: #999;">If you didn’t request this, you can safely ignore this email.</p>
            <hr>
            <p style="font-size: 12px; color: #aaa;">&copy; 2025 Your Company. All rights reserved.</p>
        </div>
    </body>
    </html>
    """

    msg = EmailMultiAlternatives(subject, text_content, from_email, [to_email])
    msg.attach_alternative(html_content, "text/html")
    msg.send()