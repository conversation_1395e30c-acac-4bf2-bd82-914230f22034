from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ..serializers.RegisterSerializer import RegisterSerializer
from ..utils import send_activation_email, send_reset_password_email

class RegisterAPIView(APIView):
    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            send_activation_email(user, request) 
            return Response({"message": "User registered successfully."}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

##################################################################################
##################################################################################
##################################################################################
from django.utils.http import urlsafe_base64_decode
from django.contrib.auth.tokens import default_token_generator
from django.utils.timezone import now
from datetime import timedelta
from django.contrib.auth import get_user_model
User = get_user_model()

class ActivateAccountView(APIView):
    def get(self, request, uidb64, token):
        try:
            uid = urlsafe_base64_decode(uidb64).decode()
            CurrUser = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            CurrUser = None
        
        if CurrUser:
            if default_token_generator.check_token(CurrUser, token):
                token_created_time = CurrUser.date_joined
                if now() - token_created_time > timedelta(hours=24):
                    return Response({"error": "Activation link has expired."}, status=status.HTTP_400_BAD_REQUEST)

                if CurrUser.is_email_verified:
                    return Response({"message": "Account is already activated."}, status=status.HTTP_200_OK)

                CurrUser.is_email_verified = True
                CurrUser.save()
                return Response({"message": "Account activated successfully."}, status=status.HTTP_200_OK)

        return Response({"error": "Invalid or expired activation link."}, status=status.HTTP_400_BAD_REQUEST)
    
##################################################################################
##################################################################################
##################################################################################
from ..serializers.AccountLoginSerializer import AccountLoginSerializer
from rest_framework_simplejwt.tokens import RefreshToken

class LoginAPIView(APIView):
    def post(self, request):
        serializer = AccountLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            refresh = RefreshToken.for_user(user)

            return Response({
                "refresh": str(refresh),
                "access": str(refresh.access_token),
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "fullname": user.first_name,
                    "phone": user.phone,
                    "role": user.role,
                    "is_superuser": user.is_superuser,
                }
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
##################################################################################
##################################################################################
##################################################################################
from rest_framework.permissions import IsAuthenticated,AllowAny
from ..serializers.user_serializer import UserSerializer

class GetUserProfileAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
##################################################################################
##################################################################################
##################################################################################
from ..serializers.UserUpdateSerializer import UserUpdateSerializer

class UpdateUserProfileAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request):
        serializer = UserUpdateSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

##################################################################################
##################################################################################
##################################################################################
from ..serializers.ForgotPasswordSerializer import ForgotPasswordSerializer

class ForgotPasswordAPIView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = ForgotPasswordSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        email = serializer.validated_data["email"]

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response(
                {"message": "If this email is registered, a reset link has been sent."},
                status=status.HTTP_200_OK
            )

        try:
            send_reset_password_email(user, request)
        except Exception as e:
            return Response(
                {
                    "error": "Failed to send reset email.",
                    "details": str(e)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        return Response(
            {"message": "Password reset email sent successfully."},
            status=status.HTTP_200_OK
        )

##################################################################################
##################################################################################
##################################################################################
from ..serializers.ResetPasswordSerializer import ResetPasswordSerializer

class ResetPasswordAPIView(APIView):
    def post(self, request, uidb64, token):
        data = {
            "uidb64": uidb64,
            "token": token,
            "new_password": request.data.get("new_password"),
            "confirm_password": request.data.get("confirm_password")
        }

        serializer = ResetPasswordSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Password reset successfully."}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)