from rest_framework import serializers
from ..models import User
from django.contrib.auth import authenticate

class AccountLoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField(write_only=True)

    def validate(self, data):
        email = data.get('email')
        password = data.get('password')

        if email and password:
            user = authenticate(email=email, password=password)

            if not user:
                raise serializers.ValidationError("Invalid email or password.")

            if not user.is_email_verified:
                raise serializers.ValidationError("Account is not activated.")

            data['user'] = user
            return data

        raise serializers.ValidationError("Must include 'email' and 'password'.")