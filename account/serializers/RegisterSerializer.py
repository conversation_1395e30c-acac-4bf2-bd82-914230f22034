from rest_framework import serializers
from ..models import User
import re

class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    role = serializers.ChoiceField(choices=User.ROLE_CHOICES)

    class Meta:
        model = User
        fields = ['fullname', 'email', 'phone', 'password', 'role', 'governorate', 'address']

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("Email is already registered.")
        return value

    def validate_phone(self, value):
        if not re.match(r'^01[0125]{9}$', value):
            raise serializers.ValidationError("Invalid phone number format.")
        if User.objects.filter(phone=value).exists():
            raise serializers.ValidationError("Phone number is already registered.")
        return value

    def validate_password(self, value):
        if len(value) < 8:
            raise serializers.ValidationError("Password must be at least 8 characters.")
        return value

    def validate(self, attrs):
        if attrs['role'] not in dict(User.ROLE_CHOICES).keys():
            raise serializers.ValidationError({"role": "Invalid role selected."})
        return attrs

    def create(self, validated_data):
        return User.objects.create_user(
            email=validated_data['email'],
            username=validated_data['email'],
            phone=validated_data['phone'],
            password=validated_data['password'],
            fullname=validated_data['fullname'],
            role=validated_data['role'],
            governorate=validated_data['governorate'],
            address=validated_data['address'],
        )
