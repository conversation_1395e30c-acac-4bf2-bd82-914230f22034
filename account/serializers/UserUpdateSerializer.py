import re
from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()

class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['fullname', 'phone', 'governorate', 'address']

    def validate_phone(self, value):
        if not re.match(r"^01[0125][0-9]{8}$", value):
            raise serializers.ValidationError("Phone number must be a valid Egyptian number (e.g., 010xxxxxxxx).")
        return value
