from rest_framework import serializers
from ..models import User

class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True, error_messages={
        "required": "Email is required.",
        "blank": "Email cannot be blank.",
        "invalid": "Enter a valid email address."
    })

    def validate_email(self, value):
        if not User.objects.filter(email=value).exists():
            raise serializers.ValidationError("No account associated with this email.")
        return value
