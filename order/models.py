from django.db import models
from django.contrib.auth import get_user_model
from core.models import BaseModel
User = get_user_model()

class Order(BaseModel):
    STATUS_CHOICES = [
        ('pending_admin', 'Pending Admin'),
        ('shipping', 'Shipping'),
        ('awaiting_confirmation', 'Awaiting Confirmation'),
        ('under_review', 'Under Review'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ]

    crop = models.ForeignKey('crop.Crop', on_delete=models.CASCADE)
    buyer = models.ForeignKey(User, on_delete=models.CASCADE)
    add_engineer_check = models.BooleanField(default=False)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    shipping_fee = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=30, choices=STATUS_CHOICES)

    def __str__(self):
        return f"Order #{self.pk} - {self.status}"



class OrderImage(BaseModel):
    IMAGE_TYPE_CHOICES = [
        ('shipping', 'Shipping'),
        ('receiving', 'Receiving'),
    ]

    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    image_type = models.CharField(max_length=20, choices=IMAGE_TYPE_CHOICES)
    image_url = models.URLField()

    def __str__(self):
        return f"Image {self.pk} - {self.image_type}"