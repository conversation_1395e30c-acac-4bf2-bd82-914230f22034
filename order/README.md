# Order API Documentation

## Farmer Orders Tracking Endpoint

### Overview
This API endpoint allows authenticated farmers to retrieve and filter orders for crops they own.

### Endpoint Details
- **URL:** `/api/order/farmer/orders`
- **Method:** `GET`
- **Authentication:** Required (<PERSON>W<PERSON>)
- **Authorization:** Farmer role required (`user.role == 'farmer'`)

### Query Parameters
All parameters are optional:

| Parameter | Type | Description | Default | Max |
|-----------|------|-------------|---------|-----|
| `status` | string | Filter orders by status | None | - |
| `page` | integer | Page number for pagination | 1 | - |
| `limit` | integer | Number of orders per page | 20 | 100 |

### Valid Status Values
The `status` parameter accepts the following values from `Order.STATUS_CHOICES`:
- `pending_admin` - Pending Admin
- `shipping` - Shipping
- `awaiting_confirmation` - Awaiting Confirmation
- `under_review` - Under Review
- `completed` - Completed
- `rejected` - Rejected

### Response Format

#### Success Response (200 OK)
```json
{
    "count": 25,
    "next": "http://localhost:8000/api/order/farmer/orders?page=2",
    "previous": null,
    "results": [
        {
            "id": 1,
            "crop": {
                "id": 1,
                "farmer": 1,
                "crop_type": {
                    "id": 1,
                    "crop_name": "Tomatoes",
                    "description": "Fresh tomatoes"
                },
                "quantity": "100.00",
                "unit": "kg",
                "price": "50.00",
                "harvest_date": "2024-01-15",
                "description": "High quality tomatoes",
                "status": "approved"
            },
            "buyer": {
                "id": 2,
                "fullname": "John Buyer",
                "email": "<EMAIL>",
                "role": "user"
            },
            "add_engineer_check": false,
            "quantity": "50.00",
            "total_amount": "2500.00",
            "shipping_fee": "100.00",
            "status": "pending_admin",
            "created_at": "2024-01-10T10:30:00Z",
            "updated_at": "2024-01-10T10:30:00Z",
            "is_active": true,
            "is_deleted": false
        }
    ]
}
```

#### Error Responses

##### Unauthorized (401)
```json
{
    "detail": "Authentication credentials were not provided."
}
```

##### Forbidden (403)
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### Usage Examples

#### Get all orders for authenticated farmer
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/order/farmer/orders
```

#### Filter orders by status
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8000/api/order/farmer/orders?status=shipping"
```

#### Paginated request
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8000/api/order/farmer/orders?page=2&limit=10"
```

#### Combined filtering and pagination
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8000/api/order/farmer/orders?status=completed&page=1&limit=5"
```

### Implementation Details

#### Security
- **Authentication:** JWT token required in Authorization header
- **Authorization:** Only users with `role='farmer'` can access this endpoint
- **Data Isolation:** Farmers can only see orders for crops they own

#### Performance Optimizations
- Uses `select_related('crop', 'buyer')` to minimize database queries
- Implements pagination to handle large datasets efficiently
- Orders are sorted by creation date (newest first)

#### Database Filtering
The endpoint filters orders using the following criteria:
- `crop__farmer = request.user` (only farmer's own crops)
- `is_active = True` (only active orders)
- `is_deleted = False` (exclude soft-deleted orders)
- Optional status filtering via query parameter

### Testing
Run the test suite with:
```bash
python manage.py test order.tests.test_farmer_orders_api
```

### Files Modified/Created
1. `order/api/__init__.py` - API module initialization
2. `order/api/views.py` - Main API view implementation
3. `order/serializer/order_serializer.py` - Enhanced serializer with relationships
4. `order/urls.py` - URL configuration
5. `order/tests/test_farmer_orders_api.py` - Comprehensive test suite

### Dependencies
- Django REST Framework
- django-filter (for status filtering)
- djangorestframework-simplejwt (for JWT authentication)
