# Order API Documentation

## Overview
This module provides API endpoints for managing orders, including buyer order history functionality.

## Buyer Order History Endpoint

### Overview
This API endpoint allows authenticated buyers to retrieve their order history from the last 30 days.

### Endpoint Details
- **URL:** `/api/order/buyer/orders`
- **Method:** `GET`
- **Authentication:** Required (JWT <PERSON>)
- **Authorization:** Buyer role required (`user.role == 'user'`)

### Query Parameters
All parameters are optional:

| Parameter | Type | Description | Default | Max |
|-----------|------|-------------|---------|-----|
| `status` | string | Filter orders by status | None | - |
| `page` | integer | Page number for pagination | 1 | - |
| `limit` | integer | Number of orders per page | 20 | 100 |

### Valid Status Values
The `status` parameter accepts the following values from `Order.STATUS_CHOICES`:
- `pending_admin` - Pending Admin
- `shipping` - Shipping
- `awaiting_confirmation` - Awaiting Confirmation
- `under_review` - Under Review
- `completed` - Completed
- `rejected` - Rejected

### Date Filtering
- Only orders created within the **last 30 days** are returned
- Date calculation is based on the `created_at` field
- Uses timezone-aware datetime operations

### Response Format

#### Success Response (200 OK)
```json
{
    "count": 15,
    "next": "http://localhost:8000/api/order/buyer/orders?page=2",
    "previous": null,
    "results": [
        {
            "id": 1,
            "crop": {
                "id": 1,
                "farmer": {
                    "id": 2,
                    "fullname": "John Farmer",
                    "email": "<EMAIL>"
                },
                "crop_type": {
                    "id": 1,
                    "crop_name": "Tomatoes",
                    "description": "Fresh tomatoes"
                },
                "quantity": "100.00",
                "unit": "kg",
                "price": "50.00",
                "harvest_date": "2024-01-15",
                "description": "High quality tomatoes",
                "status": "approved"
            },
            "buyer": {
                "id": 3,
                "fullname": "Jane Buyer",
                "email": "<EMAIL>",
                "role": "user"
            },
            "add_engineer_check": false,
            "quantity": "50.00",
            "total_amount": "2500.00",
            "shipping_fee": "100.00",
            "status": "shipping",
            "created_at": "2024-01-20T10:30:00Z",
            "updated_at": "2024-01-21T15:45:00Z",
            "is_active": true,
            "is_deleted": false
        }
    ]
}
```

#### Error Responses

##### Unauthorized (401)
```json
{
    "detail": "Authentication credentials were not provided."
}
```

##### Forbidden (403)
```json
{
    "detail": "You do not have permission to perform this action."
}
```

### Usage Examples

#### Get all recent orders for authenticated buyer
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/order/buyer/orders
```

#### Filter orders by status
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8000/api/order/buyer/orders?status=shipping"
```

#### Paginated request
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8000/api/order/buyer/orders?page=2&limit=10"
```

#### Combined filtering and pagination
```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     "http://localhost:8000/api/order/buyer/orders?status=completed&page=1&limit=5"
```

### Implementation Details

#### Security
- **Authentication:** JWT token required in Authorization header
- **Authorization:** Only users with `role='user'` (buyers) can access this endpoint
- **Data Isolation:** Buyers can only see their own orders

#### Performance Optimizations
- Uses `select_related('crop', 'crop__farmer')` to minimize database queries
- Implements pagination to handle large datasets efficiently
- Orders are sorted by creation date (newest first)
- Efficient date filtering using database-level operations

#### Database Filtering
The endpoint filters orders using the following criteria:
- `buyer = request.user` (only buyer's own orders)
- `created_at >= 30 days ago` (only recent orders)
- `is_active = True` (only active orders)
- `is_deleted = False` (exclude soft-deleted orders)
- Optional status filtering via query parameter

### Business Rules
- **30-Day Limit:** Only orders from the last 30 days are shown
- **Buyer Isolation:** Each buyer can only see their own orders
- **Active Orders Only:** Soft-deleted or inactive orders are excluded
- **Chronological Order:** Results are ordered by creation date (newest first)

### Testing
Run the test suite with:
```bash
# Run all buyer order history tests
python manage.py test order.tests.test_buyer_order_history_api

# Run specific test methods
python manage.py test order.tests.test_buyer_order_history_api.BuyerOrderHistoryAPITestCase.test_thirty_day_filtering
```

### Files Created
1. `order/api/__init__.py` - API module initialization
2. `order/api/views.py` - Main API view implementation:
   - `IsBuyerUser` - Custom permission class for buyer authentication
   - `OrderPagination` - Custom pagination class
   - `BuyerOrderHistoryView` - Main endpoint implementation
3. `order/urls.py` - URL configuration
4. `order/tests/test_buyer_order_history_api.py` - Comprehensive test suite
5. `order/README.md` - API documentation

### Dependencies
- Django REST Framework
- django-filter (for status filtering)
- djangorestframework-simplejwt (for JWT authentication)
- Django timezone utilities (for date filtering)

### Error Handling
- **Authentication Errors:** Proper 401 responses for missing/invalid tokens
- **Authorization Errors:** 403 responses for non-buyer users
- **Validation Errors:** Graceful handling of invalid query parameters
- **Empty Results:** Proper response format when no orders are found
