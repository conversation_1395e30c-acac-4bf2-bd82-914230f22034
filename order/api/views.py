from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django_filters.rest_framework import DjangoFilterBackend
from django.db import transaction
from django.shortcuts import get_object_or_404

from ..models import Order, OrderImage
from ..serializer.order_serializer import OrderSerializer, ShipOrderSerializer

User = get_user_model()


class IsFarmerUser(IsAuthenticated):
    """
    Custom permission to only allow farmer users to access the view.
    """
    def has_permission(self, request, view):
        return (
            super().has_permission(request, view) and
            request.user.role == 'farmer'
        )


class OrderPagination(PageNumberPagination):
    """
    Custom pagination for order endpoints
    """
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100
    page_query_param = 'page'


class FarmerOrdersListView(generics.ListAPIView):
    """
    GET /api/order/farmer/orders
    
    Returns a paginated list of orders for the authenticated farmer, 
    filtered by status if provided.
    Farmer users only.
    
    Query Parameters:
    - status (optional, string): Filter orders by status
    - page (optional, integer): Page number for pagination
    - limit (optional, integer): Number of results per page (max 100)
    """
    serializer_class = OrderSerializer
    permission_classes = [IsFarmerUser]
    pagination_class = OrderPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']
    
    def get_queryset(self):
        """
        Return orders for crops owned by the authenticated farmer
        """
        return Order.objects.filter(
            crop__farmer=self.request.user,
            is_active=True,
            is_deleted=False
        ).select_related('crop', 'buyer').order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'message': 'Orders retrieved successfully',
            'data': serializer.data
        })


class UpdateOrderToShippedView(generics.UpdateAPIView):
    """
    PATCH /api/order/farmer/orders/{orderId}/ship

    Updates an order status to 'shipping' and optionally adds shipping images.
    Only the farmer who owns the crop can update the order.
            
    Path Parameters:
    - orderId (required, integer): The ID of the order to update

    Request Body:
    - tracking_info (optional, object): Tracking information
    - images (optional, array): Array of shipping images
    """
    serializer_class = ShipOrderSerializer
    permission_classes = [IsFarmerUser]
    lookup_field = 'id'
    lookup_url_kwarg = 'orderId'

    def get_queryset(self):
        """
        Return orders for crops owned by the authenticated farmer
        """
        return Order.objects.filter(
            crop__farmer=self.request.user,
            is_active=True,
            is_deleted=False
        ).select_related('crop', 'buyer')

    def get_object(self):
        """
        Get the order object with additional validation
        """
        queryset = self.get_queryset()
        order_id = self.kwargs.get(self.lookup_url_kwarg)

        order = get_object_or_404(queryset, id=order_id)

        # Check if order can be shipped
        valid_statuses = ['pending_admin', 'awaiting_confirmation', 'under_review']
        if order.status not in valid_statuses:
            from rest_framework.exceptions import ValidationError
            raise ValidationError({
                'error': f'Order cannot be shipped from current status: {order.status}',
                'current_status': order.status,
                'valid_statuses': valid_statuses
            })

        return order

    def update(self, request, *args, **kwargs):
        """
        Update order status to shipping with optional tracking info and images
        """
        order = self.get_object()
        serializer = self.get_serializer(data=request.data)

        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': 'Invalid data provided',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data

        try:
            with transaction.atomic():
                # Update order status
                order.status = 'shipping'
                order.save(update_fields=['status', 'updated_at'])

                # Create shipping images if provided
                images_data = validated_data.get('images', [])
                created_images = []

                for image_data in images_data:
                    order_image = OrderImage.objects.create(
                        order=order,
                        uploaded_by=request.user,
                        image_type='shipping',
                        image_url=image_data['image_url']
                    )
                    created_images.append(order_image)

                # Serialize the updated order
                order_serializer = OrderSerializer(order)

                return Response({
                    'success': True,
                    'message': 'Order status updated to shipping successfully',
                    'data': {
                        'order': order_serializer.data,
                        'tracking_info': validated_data.get('tracking_info', {}),
                        'images_uploaded': len(created_images)
                    }
                }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'success': False,
                'message': 'Failed to update order status',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
