from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django_filters.rest_framework import DjangoFilterBackend

from ..models import Order
from ..serializer.order_serializer import OrderSerializer

User = get_user_model()


class IsFarmerUser(IsAuthenticated):
    """
    Custom permission to only allow farmer users to access the view.
    """
    def has_permission(self, request, view):
        return (
            super().has_permission(request, view) and
            request.user.role == 'farmer'
        )


class OrderPagination(PageNumberPagination):
    """
    Custom pagination for order endpoints
    """
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100
    page_query_param = 'page'


class FarmerOrdersListView(generics.ListAPIView):
    """
    GET /api/order/farmer/orders
    
    Returns a paginated list of orders for the authenticated farmer, 
    filtered by status if provided.
    Farmer users only.
    
    Query Parameters:
    - status (optional, string): Filter orders by status
    - page (optional, integer): Page number for pagination
    - limit (optional, integer): Number of results per page (max 100)
    """
    serializer_class = OrderSerializer
    permission_classes = [IsFarmerUser]
    pagination_class = OrderPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']
    
    def get_queryset(self):
        """
        Return orders for crops owned by the authenticated farmer
        """
        return Order.objects.filter(
            crop__farmer=self.request.user,
            is_active=True,
            is_deleted=False
        ).select_related('crop', 'buyer').order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'message': 'Orders retrieved successfully',
            'data': serializer.data
        })
