from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.contrib.auth import get_user_model
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from ..models import Order
from ..serializer.order_serializer import OrderSerializer

User = get_user_model()


class IsBuyerUser(IsAuthenticated):
    """
    Custom permission to only allow buyer users to access the view.
    Buyers have role='user' in the system.
    """
    def has_permission(self, request, view):
        return (
            super().has_permission(request, view) and
            request.user.role == 'user'
        )


class OrderPagination(PageNumberPagination):
    """
    Custom pagination for order endpoints
    """
    page_size = 20
    page_size_query_param = 'limit'
    max_page_size = 100
    page_query_param = 'page'


class BuyerOrderHistoryView(generics.ListAPIView):
    """
    GET /api/order/buyer/orders
    
    Returns a paginated list of orders for the authenticated buyer from the last 30 days,
    filtered by status if provided.
    Buyer users only (role='user').
    
    Query Parameters:
    - status (optional, string): Filter orders by status
    - page (optional, integer): Page number for pagination
    - limit (optional, integer): Number of results per page (max 100)
    """
    serializer_class = OrderSerializer
    permission_classes = [IsBuyerUser]
    pagination_class = OrderPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['status']
    
    def get_queryset(self):
        """
        Return orders for the authenticated buyer from the last 30 days
        """
        # Calculate date 30 days ago
        thirty_days_ago = timezone.now() - timedelta(days=30)
        
        return Order.objects.filter(
            buyer=self.request.user,
            created_at__gte=thirty_days_ago,
            is_active=True,
            is_deleted=False
        ).select_related('crop', 'crop__farmer').order_by('-created_at')
    
    def list(self, request, *args, **kwargs):
        """
        Override list method to provide custom response format
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'message': 'Order history retrieved successfully',
            'data': serializer.data
        })
