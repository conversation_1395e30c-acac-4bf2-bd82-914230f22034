"""
Test cases for the Buyer Order History API endpoint.

This file contains test cases to verify the functionality of the 
/api/order/buyer/orders endpoint including:
- Authentication and authorization (buyer role required)
- 30-day date filtering
- Status filtering
- Pagination
- Data filtering (only buyer's own orders)
"""

from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken

from crop.models import Crop, CropType
from order.models import Order

User = get_user_model()


class BuyerOrderHistoryAPITestCase(TestCase):
    """Test cases for the Buyer Order History API endpoint"""
    
    def setUp(self):
        """Set up test data"""
        # Create test users
        self.buyer1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test Buyer 1',
            phone='1234567890',
            role='user',  # Buyers have role 'user'
            governorate='Test Gov',
            address='Test Address',
            is_email_verified=True
        )
        
        self.buyer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test Buyer 2',
            phone='1234567891',
            role='user',
            governorate='Test Gov',
            address='Test Address',
            is_email_verified=True
        )
        
        self.farmer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test Farmer',
            phone='1234567892',
            role='farmer',
            governorate='Test Gov',
            address='Test Address',
            is_email_verified=True
        )
        
        self.admin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='Test Admin',
            phone='1234567893',
            role='admin',
            governorate='Test Gov',
            address='Test Address',
            is_email_verified=True
        )
        
        # Create crop type
        self.crop_type = CropType.objects.create(
            crop_name='Test Crop',
            description='Test crop description'
        )
        
        # Create crop
        self.crop = Crop.objects.create(
            farmer=self.farmer,
            crop_type=self.crop_type,
            quantity=100.00,
            unit='kg',
            price=50.00,
            harvest_date='2024-01-01',
            description='Test crop',
            status='approved'
        )
        
        # Create orders with different dates and statuses
        now = timezone.now()
        
        # Recent orders (within 30 days)
        self.recent_order1 = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer1,
            quantity=50.00,
            total_amount=2500.00,
            shipping_fee=100.00,
            status='pending_admin'
        )
        self.recent_order1.created_at = now - timedelta(days=5)
        self.recent_order1.save()
        
        self.recent_order2 = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer1,
            quantity=30.00,
            total_amount=1500.00,
            shipping_fee=100.00,
            status='shipping'
        )
        self.recent_order2.created_at = now - timedelta(days=15)
        self.recent_order2.save()
        
        # Old order (more than 30 days ago)
        self.old_order = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer1,
            quantity=20.00,
            total_amount=1000.00,
            shipping_fee=100.00,
            status='completed'
        )
        self.old_order.created_at = now - timedelta(days=35)
        self.old_order.save()
        
        # Order for different buyer
        self.other_buyer_order = Order.objects.create(
            crop=self.crop,
            buyer=self.buyer2,
            quantity=40.00,
            total_amount=2000.00,
            shipping_fee=100.00,
            status='pending_admin'
        )
        self.other_buyer_order.created_at = now - timedelta(days=10)
        self.other_buyer_order.save()
        
        # Set up API client
        self.client = APIClient()
        self.url = reverse('order:buyer-order-history')
    
    def get_jwt_token(self, user):
        """Helper method to get JWT token for user"""
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    
    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated users cannot access the endpoint"""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_non_buyer_access_denied(self):
        """Test that non-buyer users cannot access the endpoint"""
        # Test with farmer
        token = self.get_jwt_token(self.farmer)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Test with admin
        token = self.get_jwt_token(self.admin)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_buyer_can_access_own_orders(self):
        """Test that buyers can access their own order history"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should return 2 recent orders for buyer1 (excluding old order)
        self.assertEqual(len(response.data['results']), 2)
    
    def test_buyer_only_sees_own_orders(self):
        """Test that buyers only see their own orders"""
        token = self.get_jwt_token(self.buyer2)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should return 1 order for buyer2
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['id'], self.other_buyer_order.id)
    
    def test_thirty_day_filtering(self):
        """Test that only orders from the last 30 days are returned"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should return 2 recent orders, not the old one
        self.assertEqual(len(response.data['results']), 2)
        
        # Verify the old order is not included
        order_ids = [order['id'] for order in response.data['results']]
        self.assertNotIn(self.old_order.id, order_ids)
        self.assertIn(self.recent_order1.id, order_ids)
        self.assertIn(self.recent_order2.id, order_ids)
    
    def test_status_filtering(self):
        """Test filtering orders by status"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Filter by 'shipping' status
        response = self.client.get(self.url, {'status': 'shipping'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['status'], 'shipping')
        self.assertEqual(response.data['results'][0]['id'], self.recent_order2.id)
    
    def test_pagination(self):
        """Test pagination functionality"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Test with limit=1
        response = self.client.get(self.url, {'limit': 1})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertIsNotNone(response.data['next'])
    
    def test_ordering_by_created_at_desc(self):
        """Test that orders are ordered by creation date (newest first)"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Should return orders with newest first
        orders = response.data['results']
        self.assertEqual(orders[0]['id'], self.recent_order1.id)  # 5 days ago
        self.assertEqual(orders[1]['id'], self.recent_order2.id)  # 15 days ago
    
    def test_response_format(self):
        """Test that the response has the correct format"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check pagination structure
        self.assertIn('count', response.data)
        self.assertIn('next', response.data)
        self.assertIn('previous', response.data)
        self.assertIn('results', response.data)
        
        # Check order structure
        if response.data['results']:
            order = response.data['results'][0]
            expected_fields = [
                'id', 'crop', 'buyer', 'add_engineer_check',
                'quantity', 'total_amount', 'shipping_fee', 'status',
                'created_at', 'updated_at', 'is_active', 'is_deleted'
            ]
            for field in expected_fields:
                self.assertIn(field, order)
            
            # Check that crop information includes farmer details
            self.assertIn('farmer', order['crop'])
    
    def test_empty_result_for_no_recent_orders(self):
        """Test response when buyer has no orders in the last 30 days"""
        # Create a buyer with no recent orders
        buyer_no_orders = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            fullname='No Orders Buyer',
            phone='9999999999',
            role='user',
            governorate='Test Gov',
            address='Test Address',
            is_email_verified=True
        )
        
        token = self.get_jwt_token(buyer_no_orders)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)
        self.assertEqual(response.data['count'], 0)
    
    def test_invalid_status_filter(self):
        """Test filtering with invalid status value"""
        token = self.get_jwt_token(self.buyer1)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
        
        # Filter by invalid status
        response = self.client.get(self.url, {'status': 'invalid_status'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should return empty results for invalid status
        self.assertEqual(len(response.data['results']), 0)
