from rest_framework import serializers
from django.contrib.auth import get_user_model
from ..models import Order, OrderImage

User = get_user_model()


class OrderSerializer(serializers.ModelSerializer):
    # Use nested serializers to avoid circular imports
    crop = serializers.SerializerMethodField()
    buyer = serializers.SerializerMethodField()

    def get_crop(self, obj):
        """Get crop details"""
        return {
            'id': obj.crop.id,
            'farmer': {
                'id': obj.crop.farmer.id,
                'fullname': obj.crop.farmer.fullname,
                'email': obj.crop.farmer.email,
            },
            'crop_type': {
                'id': obj.crop.crop_type.id,
                'crop_name': obj.crop.crop_type.crop_name,
                'description': obj.crop.crop_type.description,
            },
            'quantity': str(obj.crop.quantity),
            'unit': obj.crop.unit,
            'price': str(obj.crop.price),
            'harvest_date': obj.crop.harvest_date,
            'description': obj.crop.description,
            'status': obj.crop.status,
        }

    def get_buyer(self, obj):
        """Get buyer details"""
        return {
            'id': obj.buyer.id,
            'fullname': obj.buyer.fullname,
            'email': obj.buyer.email,
            'role': obj.buyer.role,
        }

    class Meta:
        model = Order
        fields = [
            'id',
            'crop',
            'buyer',
            'add_engineer_check',
            'quantity',
            'total_amount',
            'shipping_fee',
            'status',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted'
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }


class OrderImageSerializer(serializers.ModelSerializer):
    uploaded_by = serializers.SerializerMethodField()

    def get_uploaded_by(self, obj):
        """Get uploader details"""
        return {
            'id': obj.uploaded_by.id,
            'fullname': obj.uploaded_by.fullname,
            'email': obj.uploaded_by.email,
            'role': obj.uploaded_by.role,
        }

    class Meta:
        model = OrderImage
        fields = [
            'id',
            'order',
            'uploaded_by',
            'image_type',
            'image_url',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted'
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }