from django.db import models
from core.models import BaseModel
from account.models import User
from order.models import Order
from payment.models import Payment


class Wallet(BaseModel):
    userid = models.ForeignKey(User, on_delete=models.CASCADE)
    balance = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
   

    def __str__(self):
        return f"Wallet {self.id} - User {self.userid_id}"


class WalletTransactions(BaseModel):
    TRANSACTION_TYPES = [
        ('deposit', 'Deposit'),
        ('withdrawal', 'Withdrawal'),
        ('payment', 'Payment'),
        ('refund', 'Refund'),
        ('transfer', 'Transfer'),
    ]
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('reversed', 'Reversed'),
    ]

    walletid = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    userid = models.ForeignKey(User, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    related_orderid = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True)
    related_paymentid = models.ForeignKey(Payment, on_delete=models.SET_NULL, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    balance_before = models.DecimalField(max_digits=12, decimal_places=2)
    balance_after = models.DecimalField(max_digits=12, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES)
 

    def __str__(self):
        return f"Transaction {self.id} - Wallet {self.walletid_id}"
