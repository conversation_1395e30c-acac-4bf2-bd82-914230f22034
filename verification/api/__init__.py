from .views import (
    UnverifiedUsersListView,
    UserVerificationDetailView
)

__all__ = [
    'UnverifiedUsersListView',
    'UserVerificationDetailView'
]

"""
from verification.api import *
Only the names listed in __all__ will be imported in that case.

So if you don’t use __all__, Python will import everything not starting with underscore.
But by defining __all__, you're saying explicitly what to expose.

"""