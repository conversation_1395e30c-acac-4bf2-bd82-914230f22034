from rest_framework import serializers
from django.contrib.auth import get_user_model
from ..models import Verification, UserVerificationStatus

User = get_user_model()


class VerificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Verification
        fields = [
            'id',
            'userid',
            'nationalid',
            'image_url',
            'type',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted',
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }


class UserVerificationStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserVerificationStatus
        fields = ['status', 'rejection_reason']
        extra_kwargs = {
            'rejection_reason': {'required': False, 'allow_null': True}
        }


# Admin-specific serializers for verification management
class UnverifiedUserSerializer(serializers.ModelSerializer):
    """Serializer for listing unverified users (admin only)"""
    verification_status = serializers.SerializerMethodField()
    registration_date = serializers.DateTimeField(source='created_at', read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'fullname',
            'email',
            'phone',
            'role',
            'governorate',
            'address',
            'registration_date',
            'verification_status',
            'is_verified'
        ]
        read_only_fields = ['id', 'registration_date', 'verification_status', 'is_verified']

    def get_verification_status(self, obj):
        """Get the current verification status for the user"""
        try:
            status = obj.verification_status
            return {
                'status': status.status,
                'rejection_reason': status.rejection_reason
            }
        except UserVerificationStatus.DoesNotExist:
            return {
                'status': 'pending',
                'rejection_reason': None
            }


class UserVerificationDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for user verification review (admin only)"""
    verification_documents = serializers.SerializerMethodField()
    verification_status = serializers.SerializerMethodField()
    registration_date = serializers.DateTimeField(source='created_at', read_only=True)

    class Meta:
        model = User
        fields = [
            'id',
            'fullname',
            'email',
            'phone',
            'role',
            'governorate',
            'address',
            'registration_date',
            'is_verified',
            'verification_status',
            'verification_documents'
        ]
        read_only_fields = ['id', 'registration_date', 'is_verified']

    def get_verification_documents(self, obj):
        """Get all verification documents for the user"""
        verifications = obj.verifications.filter(is_active=True, is_deleted=False)
        return VerificationSerializer(verifications, many=True).data

    def get_verification_status(self, obj):
        """Get the current verification status for the user"""
        try:
            status = obj.verification_status
            return {
                'status': status.status,
                'rejection_reason': status.rejection_reason,
                'created_at': status.created_at,
                'updated_at': status.updated_at
            }
        except UserVerificationStatus.DoesNotExist:
            return {
                'status': 'pending',
                'rejection_reason': None,
                'created_at': None,
                'updated_at': None
            }


class VerificationStatusUpdateSerializer(serializers.Serializer):
    """Serializer for updating user verification status (admin only)"""
    STATUS_CHOICES = [
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]

    status = serializers.ChoiceField(choices=STATUS_CHOICES, required=True)
    rejection_reason = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="Required if status is 'rejected'"
    )

    def validate(self, data):
        """Validate that rejection_reason is provided when status is rejected"""
        status = data.get('status')
        rejection_reason = data.get('rejection_reason')

        if status == 'rejected' and not rejection_reason:
            raise serializers.ValidationError({
                'rejection_reason': 'Rejection reason is required when status is rejected.'
            })

        if status == 'approved' and rejection_reason:
            # Clear rejection reason if status is approved
            data['rejection_reason'] = None

        return data
