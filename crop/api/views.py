from rest_framework import generics, filters
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from crop.models import Crop, CropType
from crop.serializers.crop_serializer import CropSerializer

class LatestCropsView(generics.ListAPIView):
    serializer_class = CropSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        return Crop.objects.filter(is_active=True).order_by('-created_at')[:10]

class FeaturedCropsView(generics.ListAPIView):
    serializer_class = CropSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    
    def get_queryset(self):
        return Crop.objects.filter(
            is_active=True,
            status='approved'
        ).order_by('-created_at')[:10]

class AllCropsView(generics.ListAPIView):
    serializer_class = CropSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    
    filterset_fields = {
        'crop_type': ['exact'],
        'farmer': ['exact'],
        'status': ['exact'],
        'price': ['gte', 'lte'],
        'harvest_date': ['gte', 'lte'],
        'quantity': ['gte', 'lte'],
    }
    
    search_fields = ['crop_type__crop_name', 'description']
    ordering_fields = ['price', 'harvest_date', 'created_at']
    ordering = ['-created_at']
    
    def get_queryset(self):
        queryset = Crop.objects.filter(is_active=True)
        
        crop_type_name = self.request.query_params.get('crop_type_name')
        if crop_type_name:
            queryset = queryset.filter(crop_type__crop_name__icontains=crop_type_name)
            
        return queryset