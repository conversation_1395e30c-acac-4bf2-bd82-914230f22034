from django.db import models
from django.conf import settings
from core.models import BaseModel

# Model for crop_type table
class CropType(BaseModel):
    id = models.AutoField(primary_key=True)
    crop_name = models.CharField(max_length=255)
    description = models.TextField()

    class Meta:
        db_table = 'crop_type'

    def __str__(self):
        return self.crop_name

# Model for crop table
class Crop(BaseModel):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('sold', 'Sold'),
    )

    UNIT_CHOICES = (
        ('kg', 'Kilogram'),
        ('ton', 'Ton'),
        ('box', 'Box'),
    )

    id = models.AutoField(primary_key=True)
    farmer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='crops')
    crop_type = models.ForeignKey(CropType, on_delete=models.CASCADE, related_name='crops')
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.CharField(max_length=10, choices=UNIT_CHOICES)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    harvest_date = models.DateField()
    description = models.TextField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    class Meta:
        db_table = 'crop'

    def __str__(self):
        return f"{self.crop_type.crop_name} - {self.farmer.username}"

# Model for cropimage table
class CropImage(BaseModel):
    id = models.AutoField(primary_key=True)
    crop = models.ForeignKey(Crop, on_delete=models.CASCADE, related_name='images')
    image_url = models.URLField(max_length=500)

    class Meta:
        db_table = 'cropimage'

    def __str__(self):
        return f"Image for {self.crop}"