from rest_framework import serializers
from ..models import CropType, Crop, CropImage
from django.contrib.auth.models import User

# Serializer for CropType
class CropTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CropType
        fields = ['id', 'crop_name', 'description', 'created_at', 'updated_at', 'is_active', 'is_deleted']

# Serializer for CropImage
class CropImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = CropImage
        fields = ['id', 'crop', 'image_url', 'created_at', 'updated_at', 'is_active', 'is_deleted']

# Serializer for Crop
class CropSerializer(serializers.ModelSerializer):
    crop_type = CropTypeSerializer(read_only=True)
    images = CropImageSerializer(many=True, read_only=True)
    farmer = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())

    class Meta:
        model = Crop
        fields = [
            'id', 'farmer', 'crop_type', 'quantity', 'unit', 'price', 
            'harvest_date', 'description', 'status', 'created_at', 
            'updated_at', 'is_active', 'is_deleted', 'images'
        ]