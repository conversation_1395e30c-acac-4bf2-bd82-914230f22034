from django.db import models
from core.models import BaseModel

# Create your models here.
class ShippingCompany(BaseModel):
    shipper_name = models.CharField(max_length=255)

    def __str__(self):
        return self.shipper_name
    
class Shipping(BaseModel):
    SHIPPING_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_transit', 'In Transit'),
        ('delivered', 'Delivered'),
    ]

    orderid = models.ForeignKey(
        'order.Order',
        on_delete=models.CASCADE,
    )
    compid = models.ForeignKey(
        ShippingCompany,
        on_delete=models.CASCADE,
    )
    tracking_number = models.CharField(max_length=255, null=True, blank=True)
    estimated_delivery = models.DateField(null=True, blank=True)
    actual_delivery = models.DateField(null=True, blank=True)
    shipping_status = models.CharField(max_length=20, choices=SHIPPING_STATUS_CHOICES)

    def __str__(self):
        return f"{self.compid.shipper_name} - {self.shipping_status}"