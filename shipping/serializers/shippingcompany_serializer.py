from rest_framework import serializers
from ..models import ShippingCompany

class ShippingCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = ShippingCompany
        fields = [
            'id',
            'shipper_name',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted',
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }