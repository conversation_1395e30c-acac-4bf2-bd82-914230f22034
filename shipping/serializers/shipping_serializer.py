from rest_framework import serializers
from ..models import Shipping

class ShippingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Shipping
        fields = [
            'id',
            'orderid',
            'compid',
            'tracking_number',
            'estimated_delivery',
            'actual_delivery',
            'shipping_status',
            'created_at',
            'updated_at',
            'is_active',
            'is_deleted',
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
            'is_active': {'read_only': True},
            'is_deleted': {'read_only': True},
        }