from django.db import models
from core.models import BaseModel  
from account.models import User


class Subscription(BaseModel):
    userid = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=50, null=True, blank=True)
 

    def __str__(self):
        return f"Subscription {self.id} - User {self.userid}"

class SubTransactions(BaseModel):
    subid = models.ForeignKey(Subscription, on_delete=models.CASCADE)
    status = models.CharField(max_length=50, null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
   
    def __str__(self):
        return f"Transaction {self.id} for Subscription {self.subid_id}"
