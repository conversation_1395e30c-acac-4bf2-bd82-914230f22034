## 🚀 Getting Started (Locally)

```bash
# Clone the repo
git clone https://github.com/MoazAhmedS/Mahsool-back.git

# Navigate into the project
cd Mahsool-back

# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py makemigrations
python manage.py migrate

# Run server
python manage.py runserver
```

---

## 🔧 Environment Setup

This project uses environment variables to store sensitive configuration such as database credentials and email settings.

### 1. Create a `.env` file

In the root of the project (where `manage.py` is), create a `.env` file with the following content:

```env
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=your_db_host  # e.g., localhost
DB_PORT=postgres_port_number

EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password
```
